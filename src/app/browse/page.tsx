"use client"

import { useState, useEffect } from "react"
import { useGetNovelsQuery } from "@/store/api/novelsApi"
import { NovelCard } from "@/components/novel/novel-card"
import { SearchBar } from "@/components/common/search-bar"
import { GenreFilter } from "@/components/novel/genre-filter"
import { Pagination } from "@/components/common/pagination"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, Filter, BookOpen, SortAsc, SortDesc } from "lucide-react"

export default function BrowsePage() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState("")
  const [genre, setGenre] = useState("")
  const [sortBy, setSortBy] = useState("created_at")
  const [sortOrder, setSortOrder] = useState("desc")
  const [tags, setTags] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)

  const { data, isLoading, error } = useGetNovelsQuery({
    page,
    limit: 12,
    search: search || undefined,
    genre: genre || undefined,
    sortBy,
    sortOrder,
    tags: tags.length > 0 ? tags : undefined,
  })

  const handleSearch = (searchTerm: string) => {
    setSearch(searchTerm)
    setPage(1) // Reset to first page when searching
  }

  const handleGenreChange = (selectedGenre: string) => {
    setGenre(selectedGenre)
    setPage(1) // Reset to first page when filtering
  }

  const handleSortChange = (newSortBy: string) => {
    setSortBy(newSortBy)
    setPage(1) // Reset to first page when sorting
  }

  const handleSortOrderChange = (newSortOrder: string) => {
    setSortOrder(newSortOrder)
    setPage(1) // Reset to first page when sorting
  }

  const handleTagsChange = (newTags: string[]) => {
    setTags(newTags)
    setPage(1) // Reset to first page when filtering
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleClearFilters = () => {
    setSearch("")
    setGenre("")
    setTags([])
    setSortBy("created_at")
    setSortOrder("desc")
    setPage(1)
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Error Loading Novels
            </CardTitle>
            <CardDescription>
              We couldn't load the novels. Please try again later.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">Browse Novels</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover amazing stories from talented authors around the world
          </p>
        </div>

        {/* Search and Filters */}
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1 w-full">
              <SearchBar
                placeholder="Search novels, authors, or genres..."
                onSearch={handleSearch}
                value={search}
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </div>

          {showFilters && (
            <div className="bg-muted/50 rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Genre Filter */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Genre</label>
                  <GenreFilter
                    selectedGenre={genre}
                    onGenreChange={handleGenreChange}
                  />
                </div>

                {/* Sort By */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Sort By</label>
                  <Select value={sortBy} onValueChange={handleSortChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sort by..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="created_at">Newest First</SelectItem>
                      <SelectItem value="title">Title A-Z</SelectItem>
                      <SelectItem value="author">Author A-Z</SelectItem>
                      <SelectItem value="updated_at">Recently Updated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort Order */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Order</label>
                  <Select value={sortOrder} onValueChange={handleSortOrderChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="desc">
                        <div className="flex items-center gap-2">
                          <SortDesc className="h-4 w-4" />
                          Descending
                        </div>
                      </SelectItem>
                      <SelectItem value="asc">
                        <div className="flex items-center gap-2">
                          <SortAsc className="h-4 w-4" />
                          Ascending
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Clear Filters Button */}
              {(search || genre || tags.length > 0 || sortBy !== "created_at" || sortOrder !== "desc") && (
                <div className="pt-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearFilters}
                    className="w-full md:w-auto"
                  >
                    Clear All Filters
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Active Filters */}
          {(search || genre || tags.length > 0 || sortBy !== "created_at" || sortOrder !== "desc") && (
            <div className="flex flex-wrap gap-2 items-center">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {search && (
                <Badge
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => handleSearch("")}
                >
                  Search: "{search}" ×
                </Badge>
              )}
              {genre && (
                <Badge
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => handleGenreChange("")}
                >
                  Genre: {genre} ×
                </Badge>
              )}
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => handleTagsChange(tags.filter(t => t !== tag))}
                >
                  Tag: {tag} ×
                </Badge>
              ))}
              {sortBy !== "created_at" && (
                <Badge
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => handleSortChange("created_at")}
                >
                  Sort: {sortBy} ×
                </Badge>
              )}
              {sortOrder !== "desc" && (
                <Badge
                  variant="secondary"
                  className="cursor-pointer"
                  onClick={() => handleSortOrderChange("desc")}
                >
                  Order: {sortOrder} ×
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Results */}
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : data?.novels.length === 0 ? (
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                No Novels Found
              </CardTitle>
              <CardDescription>
                {search || genre
                  ? "Try adjusting your search or filters"
                  : "No novels have been published yet"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(search || genre) && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearch("")
                    setGenre("")
                  }}
                  className="w-full"
                >
                  Clear Filters
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Results Count */}
            <div className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">
                Showing {data?.novels.length || 0} of {data?.pagination.total || 0} novels
              </p>
            </div>

            {/* Novel Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {data?.novels.map((novel) => (
                <NovelCard key={novel.id} novel={novel} showLibraryButton />
              ))}
            </div>

            {/* Pagination */}
            {data && data.pagination.pages > 1 && (
              <div className="flex justify-center">
                <Pagination
                  currentPage={data.pagination.page}
                  totalPages={data.pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}