import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@/types/auth"
import { z } from "zod"

const earningsQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  type: z.enum(['SUBSCRIPTION_REVENUE', 'TIP', 'BONUS', 'REFERRAL', 'CREDIT_PURCHASE']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/earnings - Get user's earnings with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = earningsQuerySchema.parse({
      page: searchParams.get('page') || "1",
      limit: searchParams.get('limit') || "10",
      type: searchParams.get('type') || undefined,
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
    })

    const page = parseInt(query.page)
    const limit = parseInt(query.limit)
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      userId: session.user.id,
    }

    if (query.type) {
      where.type = query.type
    }

    if (query.startDate || query.endDate) {
      where.createdAt = {}
      if (query.startDate) {
        where.createdAt.gte = new Date(query.startDate)
      }
      if (query.endDate) {
        where.createdAt.lte = new Date(query.endDate)
      }
    }

    // Get earnings with pagination
    const [earnings, total] = await Promise.all([
      prisma.earning.findMany({
        where,
        include: {
          payout: {
            select: {
              id: true,
              status: true,
              processedAt: true,
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit,
      }),
      prisma.earning.count({ where })
    ])

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      data: earnings,
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      }
    })
  } catch (error) {
    console.error("Error fetching earnings:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
