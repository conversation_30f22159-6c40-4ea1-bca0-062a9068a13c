import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get("page") || "1")
  const limit = parseInt(searchParams.get("limit") || "12")
  const genre = searchParams.get("genre")
  const search = searchParams.get("search")
  const status = searchParams.get("status") || "PUBLISHED"
  const sortBy = searchParams.get("sortBy") || "created_at"
  const sortOrder = searchParams.get("sortOrder") === "asc" ? "asc" : "desc"
  const tags = searchParams.get("tags")?.split(",").filter(Boolean) || []

  const skip = (page - 1) * limit

  try {
    // Build the base query for novels with author information
    let query = supabase
      .from('novels')
      .select(`
        *,
        users!novels_author_id_fkey(id, name, image)
      `)
      .eq('status', status)
      .range(skip, skip + limit - 1)

    // Add genre filter if provided
    if (genre) {
      query = query.eq('genre', genre)
    }

    // Add tags filter if provided (novels that contain any of the specified tags)
    if (tags.length > 0) {
      query = query.overlaps('tags', tags)
    }

    // Add search filter if provided
    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`)
    }

    // Add sorting
    const sortColumn = sortBy === "author" ? "users.name" : sortBy
    query = query.order(sortColumn, { ascending: sortOrder === "asc" })

    const { data: novels, error: novelsError } = await query

    if (novelsError) {
      console.error("Error fetching novels:", novelsError)
      throw novelsError
    }

    // Get total count for pagination with same filters
    let countQuery = supabase
      .from('novels')
      .select('*', { count: 'exact', head: true })
      .eq('status', status)

    if (genre) {
      countQuery = countQuery.eq('genre', genre)
    }

    if (tags.length > 0) {
      countQuery = countQuery.overlaps('tags', tags)
    }

    if (search) {
      countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`)
    }

    const { count: total, error: countError } = await countQuery

    if (countError) {
      console.error("Error counting novels:", countError)
      throw countError
    }

    // Get chapter counts for each novel
    const novelIds = novels?.map(novel => novel.id) || []
    let chapterCounts: Record<string, number> = {}

    if (novelIds.length > 0) {
      const { data: chapters, error: chaptersError } = await supabase
        .from('chapters')
        .select('novel_id')
        .in('novel_id', novelIds)
        .eq('status', 'PUBLISHED')

      if (!chaptersError && chapters) {
        chapterCounts = chapters.reduce((acc, chapter) => {
          acc[chapter.novel_id] = (acc[chapter.novel_id] || 0) + 1
          return acc
        }, {} as Record<string, number>)
      }
    }

    // Transform snake_case to camelCase and add missing fields
    const transformedNovels = novels?.map(novel => ({
      ...novel,
      // Convert snake_case to camelCase for consistency with frontend
      createdAt: novel.created_at,
      updatedAt: novel.updated_at,
      authorId: novel.author_id,
      // Add author data from join
      author: novel.users ? {
        id: novel.users.id,
        name: novel.users.name || "Unknown",
        image: novel.users.image
      } : { id: novel.author_id, name: "Unknown", image: null },
      _count: {
        chapters: chapterCounts[novel.id] || 0
      }
    })) || []

    // Get available genres for faceted search
    const { data: genreData } = await supabase
      .from('novels')
      .select('genre')
      .eq('status', status)
      .not('genre', 'is', null)

    const genreCounts = genreData?.reduce((acc, novel) => {
      if (novel.genre) {
        acc[novel.genre] = (acc[novel.genre] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>) || {}

    const availableGenres = Object.entries(genreCounts)
      .map(([genre, count]) => ({ genre, count }))
      .sort((a, b) => b.count - a.count)

    return NextResponse.json({
      novels: transformedNovels,
      pagination: {
        page,
        limit,
        total: total || 0,
        pages: Math.ceil((total || 0) / limit),
        hasNext: page * limit < (total || 0),
        hasPrev: page > 1,
      },
      filters: {
        search,
        genre,
        status,
        tags,
        sortBy,
        sortOrder,
      },
      facets: {
        genres: availableGenres,
      },
      metadata: {
        searchTime: Date.now(),
        totalResults: total || 0,
      }
    })
  } catch (error) {
    console.error("Error fetching novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch novels" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "AUTHOR") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { title, description, synopsis, genre, tags } = body

    // Validate required fields
    if (!title || title.trim().length === 0) {
      return NextResponse.json(
        { error: "Title is required" },
        { status: 400 }
      )
    }

    const { data: novel, error } = await supabase
      .from('novels')
      .insert({
        title: title.trim(),
        description: description?.trim() || null,
        synopsis: synopsis?.trim() || null,
        genre: genre?.trim() || null,
        tags: tags || [],
        authorId: session.user.id,
        status: "DRAFT",
      })
      .select(`
        *,
        author:users!authorId(id, name, image)
      `)
      .single()

    if (error) {
      console.error("Error creating novel:", error)
      throw error
    }

    // Add chapter count (new novels have 0 chapters)
    const novelWithCount = {
      ...novel,
      _count: { chapters: 0 }
    }

    return NextResponse.json(novelWithCount, { status: 201 })
  } catch (error) {
    console.error("Error creating novel:", error)
    return NextResponse.json(
      { error: "Failed to create novel" },
      { status: 500 }
    )
  }
}