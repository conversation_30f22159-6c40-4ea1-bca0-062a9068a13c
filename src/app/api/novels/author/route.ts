import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { NovelStatus } from "@/types/auth"

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "12")
    const status = searchParams.get("status") // Optional status filter
    const search = searchParams.get("search") // Optional search term

    const skip = (page - 1) * limit

    // Build the base query for author's novels
    let query = supabase
      .from('novels')
      .select(`
        *,
        users!novels_author_id_fkey(id, name, image)
      `)
      .eq('author_id', session.user.id)
      .order('updated_at', { ascending: false })
      .range(skip, skip + limit - 1)

    // Add status filter if provided
    if (status && Object.values(NovelStatus).includes(status as any)) {
      query = query.eq('status', status)
    }

    // Add search filter if provided
    if (search && search.trim()) {
      query = query.or(`title.ilike.%${search.trim()}%,description.ilike.%${search.trim()}%,synopsis.ilike.%${search.trim()}%`)
    }

    // Execute the query
    const { data: novels, error: novelsError } = await query

    if (novelsError) {
      console.error("Error fetching author novels:", novelsError)
      throw novelsError
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('novels')
      .select('*', { count: 'exact', head: true })
      .eq('author_id', session.user.id)

    if (status && Object.values(NovelStatus).includes(status as any)) {
      countQuery = countQuery.eq('status', status)
    }

    if (search && search.trim()) {
      countQuery = countQuery.or(`title.ilike.%${search.trim()}%,description.ilike.%${search.trim()}%,synopsis.ilike.%${search.trim()}%`)
    }

    const { count: total, error: countError } = await countQuery

    if (countError) {
      console.error("Error counting author novels:", countError)
      throw countError
    }

    // Get chapter counts for each novel
    const novelIds = novels?.map(novel => novel.id) || []
    let chapterCounts: Record<string, number> = {}
    let libraryCounts: Record<string, number> = {}

    if (novelIds.length > 0) {
      // Get chapter counts
      const { data: chapters } = await supabase
        .from('chapters')
        .select('novel_id')
        .in('novel_id', novelIds)

      if (chapters) {
        chapterCounts = chapters.reduce((acc, chapter) => {
          acc[chapter.novel_id] = (acc[chapter.novel_id] || 0) + 1
          return acc
        }, {} as Record<string, number>)
      }

      // Get library counts (followers)
      const { data: libraryEntries } = await supabase
        .from('library')
        .select('novel_id')
        .in('novel_id', novelIds)

      if (libraryEntries) {
        libraryCounts = libraryEntries.reduce((acc, entry) => {
          acc[entry.novel_id] = (acc[entry.novel_id] || 0) + 1
          return acc
        }, {} as Record<string, number>)
      }
    }

    // Transform novels to match expected format
    const transformedNovels = novels?.map(novel => ({
      ...novel,
      // Convert snake_case to camelCase for consistency with frontend
      createdAt: novel.created_at,
      updatedAt: novel.updated_at,
      authorId: novel.author_id,
      // Add author data from join
      author: novel.users ? {
        id: novel.users.id,
        name: novel.users.name || "Unknown",
        image: novel.users.image
      } : { id: novel.author_id, name: "Unknown", image: null },
      _count: {
        chapters: chapterCounts[novel.id] || 0,
        library: libraryCounts[novel.id] || 0
      }
    })) || []

    // Calculate additional statistics
    const [publishedCount, draftCount, totalChaptersCount, totalFollowersCount] = await Promise.all([
      supabase.from('novels').select('*', { count: 'exact', head: true }).eq('author_id', session.user.id).eq('status', NovelStatus.PUBLISHED),
      supabase.from('novels').select('*', { count: 'exact', head: true }).eq('author_id', session.user.id).eq('status', NovelStatus.DRAFT),
      supabase.from('chapters').select('*', { count: 'exact', head: true }).in('novel_id', novelIds),
      supabase.from('library').select('*', { count: 'exact', head: true }).in('novel_id', novelIds)
    ])

    const stats = {
      total: total || 0,
      published: publishedCount.count || 0,
      draft: draftCount.count || 0,
      totalChapters: totalChaptersCount.count || 0,
      totalFollowers: totalFollowersCount.count || 0,
    }

    return NextResponse.json({
      novels: transformedNovels,
      pagination: {
        page,
        limit,
        total: total || 0,
        pages: Math.ceil((total || 0) / limit),
        hasNext: page * limit < (total || 0),
        hasPrev: page > 1,
      },
      stats,
    })
  } catch (error) {
    console.error("Error fetching author novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch author novels" },
      { status: 500 }
    )
  }
}
