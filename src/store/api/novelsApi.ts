import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Novel, NovelStatus } from '@prisma/client'
import type { NovelWithChapters } from '@/types'

export interface NovelWithAuthor extends Novel {
  author: { id: string; name: string; image?: string | null }
  _count: { chapters: number }
}

export interface NovelsResponse {
  novels: NovelWithAuthor[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
  filters?: {
    search?: string
    genre?: string
    status?: string
    tags?: string[]
    sortBy?: string
    sortOrder?: string
  }
  facets?: {
    genres: { genre: string; count: number }[]
  }
  metadata?: {
    searchTime: number
    totalResults: number
  }
}

export interface AuthorNovelsResponse {
  novels: NovelWithAuthor[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
  stats: {
    total: number
    published: number
    draft: number
    totalChapters: number
    totalFollowers: number
  }
}

export interface CreateNovelRequest {
  title: string
  description?: string
  synopsis?: string
  genre?: string
  tags?: string[]
}

export const novelsApi = createApi({
  reducerPath: 'novelsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/novels',
    credentials: 'include',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json')
      return headers
    },
  }),
  tagTypes: ['Novel', 'AuthorNovels'],
  endpoints: (builder) => ({
    // Public endpoints
    getNovels: builder.query<NovelsResponse, {
      page?: number
      limit?: number
      genre?: string
      search?: string
      status?: string
      sortBy?: string
      sortOrder?: string
      tags?: string[]
    }>({
      query: (params) => {
        const searchParams = new URLSearchParams()

        // Add all parameters to search params
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              searchParams.set(key, value.join(','))
            } else {
              searchParams.set(key, String(value))
            }
          }
        })

        return {
          url: `?${searchParams.toString()}`,
        }
      },
      providesTags: ['Novel'],
    }),

    getNovel: builder.query<NovelWithChapters, string>({
      query: (id) => `/${id}`,
      providesTags: (result, error, id) => [{ type: 'Novel', id }],
    }),

    getFeaturedNovels: builder.query<NovelWithAuthor[], void>({
      query: () => '/featured',
      providesTags: ['Novel'],
    }),

    // Author endpoints
    getAuthorNovels: builder.query<AuthorNovelsResponse, {
      page?: number
      limit?: number
      status?: string
      search?: string
    }>({
      query: (params = {}) => ({
        url: '/author',
        params,
      }),
      providesTags: ['AuthorNovels'],
    }),

    createNovel: builder.mutation<NovelWithAuthor, CreateNovelRequest>({
      query: (novel) => ({
        url: '',
        method: 'POST',
        body: novel,
      }),
      invalidatesTags: ['AuthorNovels'],
    }),

    updateNovel: builder.mutation<NovelWithAuthor, {
      id: string
      data: Partial<CreateNovelRequest>
    }>({
      query: ({ id, data }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Novel', id },
        'AuthorNovels',
      ],
    }),

    deleteNovel: builder.mutation<void, string>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['AuthorNovels'],
    }),

    publishNovel: builder.mutation<NovelWithAuthor, {
      id: string
      status: NovelStatus
    }>({
      query: ({ id, status }) => ({
        url: `/${id}/publish`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Novel', id },
        'AuthorNovels',
        'Novel',
      ],
    }),
  }),
})

export const {
  useGetNovelsQuery,
  useGetNovelQuery,
  useGetFeaturedNovelsQuery,
  useGetAuthorNovelsQuery,
  useCreateNovelMutation,
  useUpdateNovelMutation,
  useDeleteNovelMutation,
  usePublishNovelMutation,
} = novelsApi