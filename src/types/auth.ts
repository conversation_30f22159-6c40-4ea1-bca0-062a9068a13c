// Authentication and authorization types
export enum UserRole {
  READER = "READER",
  AUTHOR = "AUTHOR",
  ADMIN = "ADMIN"
}

export enum SubscriptionTier {
  FREE = "FREE",
  PREMIUM = "PREMIUM",
  PREMIUM_PLUS = "PREMIUM_PLUS"
}

export enum SubscriptionStatus {
  ACTIVE = "ACTIVE",
  CANCELED = "CANCELED",
  PAST_DUE = "PAST_DUE",
  UNPAID = "UNPAID",
  INCOMPLETE = "INCOMPLETE",
  INCOMPLETE_EXPIRED = "INCOMPLETE_EXPIRED",
  TRIALING = "TRIALING"
}

export enum PaymentStatus {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
  CANCELED = "CANCELED",
  REFUNDED = "REFUNDED"
}

export enum NovelStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED",
  COMPLETED = "COMPLETED",
  ARCHIVED = "ARCHIVED"
}

export enum ChapterStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED"
}

// Helper functions for role checking
export function isAuthor(role: string): boolean {
  return role === UserRole.AUTHOR
}

export function isReader(role: string): boolean {
  return role === UserRole.READER
}

export function isAdmin(role: string): boolean {
  return role === UserRole.ADMIN
}

export function hasAuthorAccess(role: string): boolean {
  return role === UserRole.AUTHOR || role === UserRole.ADMIN
}

export function hasAdminAccess(role: string): boolean {
  return role === UserRole.ADMIN
}
