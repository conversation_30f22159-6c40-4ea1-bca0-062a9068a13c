"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(rsc)/./node_modules/@noble/hashes/_u64.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/_u64.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.toBig = exports.shrSL = exports.shrSH = exports.rotrSL = exports.rotrSH = exports.rotrBL = exports.rotrBH = exports.rotr32L = exports.rotr32H = exports.rotlSL = exports.rotlSH = exports.rotlBL = exports.rotlBH = exports.add5L = exports.add5H = exports.add4L = exports.add4H = exports.add3L = exports.add3H = void 0;\nexports.add = add;\nexports.fromBig = fromBig;\nexports.split = split;\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\nexports.toBig = toBig;\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nexports.shrSH = shrSH;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\nexports.shrSL = shrSL;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nexports.rotrSH = rotrSH;\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\nexports.rotrSL = rotrSL;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nexports.rotrBH = rotrBH;\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\nexports.rotrBL = rotrBL;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nexports.rotr32H = rotr32H;\nconst rotr32L = (h, _l) => h;\nexports.rotr32L = rotr32L;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nexports.rotlSH = rotlSH;\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\nexports.rotlSL = rotlSL;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nexports.rotlBH = rotlBH;\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\nexports.rotlBL = rotlBL;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nexports.add3L = add3L;\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nexports.add3H = add3H;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nexports.add4L = add4L;\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nexports.add4H = add4H;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nexports.add5L = add5L;\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\nexports.add5H = add5H;\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexports[\"default\"] = u64;\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@noble/hashes/_u64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@noble/hashes/cryptoNode.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/cryptoNode.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.crypto = void 0;\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\nconst nc = __webpack_require__(/*! node:crypto */ \"node:crypto\");\nexports.crypto = nc && typeof nc === 'object' && 'webcrypto' in nc\n    ? nc.webcrypto\n    : nc && typeof nc === 'object' && 'randomBytes' in nc\n        ? nc\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9jcnlwdG9Ob2RlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtQkFBTyxDQUFDLGdDQUFhO0FBQ2hDLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL25vZGVfbW9kdWxlcy9Abm9ibGUvaGFzaGVzL2NyeXB0b05vZGUuanM/Zjg0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuY3J5cHRvID0gdm9pZCAwO1xuLyoqXG4gKiBJbnRlcm5hbCB3ZWJjcnlwdG8gYWxpYXMuXG4gKiBXZSBwcmVmZXIgV2ViQ3J5cHRvIGFrYSBnbG9iYWxUaGlzLmNyeXB0bywgd2hpY2ggZXhpc3RzIGluIG5vZGUuanMgMTYrLlxuICogRmFsbHMgYmFjayB0byBOb2RlLmpzIGJ1aWx0LWluIGNyeXB0byBmb3IgTm9kZS5qcyA8PXYxNC5cbiAqIFNlZSB1dGlscy50cyBmb3IgZGV0YWlscy5cbiAqIEBtb2R1bGVcbiAqL1xuLy8gQHRzLWlnbm9yZVxuY29uc3QgbmMgPSByZXF1aXJlKFwibm9kZTpjcnlwdG9cIik7XG5leHBvcnRzLmNyeXB0byA9IG5jICYmIHR5cGVvZiBuYyA9PT0gJ29iamVjdCcgJiYgJ3dlYmNyeXB0bycgaW4gbmNcbiAgICA/IG5jLndlYmNyeXB0b1xuICAgIDogbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAncmFuZG9tQnl0ZXMnIGluIG5jXG4gICAgICAgID8gbmNcbiAgICAgICAgOiB1bmRlZmluZWQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcnlwdG9Ob2RlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@noble/hashes/cryptoNode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@noble/hashes/sha3.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/sha3.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.shake256 = exports.shake128 = exports.keccak_512 = exports.keccak_384 = exports.keccak_256 = exports.keccak_224 = exports.sha3_512 = exports.sha3_384 = exports.sha3_256 = exports.sha3_224 = exports.Keccak = void 0;\nexports.keccakP = keccakP;\n/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\nconst _u64_ts_1 = __webpack_require__(/*! ./_u64.js */ \"(rsc)/./node_modules/@noble/hashes/_u64.js\");\n// prettier-ignore\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/@noble/hashes/utils.js\");\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst IOTAS = (0, _u64_ts_1.split)(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? (0, _u64_ts_1.rotlBH)(h, l, s) : (0, _u64_ts_1.rotlSH)(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? (0, _u64_ts_1.rotlBL)(h, l, s) : (0, _u64_ts_1.rotlSL)(h, l, s));\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nfunction keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    (0, utils_ts_1.clean)(B);\n}\n/** Keccak sponge function. */\nclass Keccak extends utils_ts_1.Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        this.enableXOF = false;\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        // Can be passed from user as dkLen\n        (0, utils_ts_1.anumber)(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        // 0 < blockLen < 200\n        if (!(0 < blockLen && blockLen < 200))\n            throw new Error('only keccak-f1600 function is supported');\n        this.state = new Uint8Array(200);\n        this.state32 = (0, utils_ts_1.u32)(this.state);\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    keccak() {\n        (0, utils_ts_1.swap32IfBE)(this.state32);\n        keccakP(this.state32, this.rounds);\n        (0, utils_ts_1.swap32IfBE)(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        (0, utils_ts_1.aexists)(this);\n        data = (0, utils_ts_1.toBytes)(data);\n        (0, utils_ts_1.abytes)(data);\n        const { blockLen, state } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        (0, utils_ts_1.aexists)(this, false);\n        (0, utils_ts_1.abytes)(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        (0, utils_ts_1.anumber)(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        (0, utils_ts_1.aoutput)(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        (0, utils_ts_1.clean)(this.state);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nexports.Keccak = Keccak;\nconst gen = (suffix, blockLen, outputLen) => (0, utils_ts_1.createHasher)(() => new Keccak(blockLen, suffix, outputLen));\n/** SHA3-224 hash function. */\nexports.sha3_224 = (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nexports.sha3_256 = (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nexports.sha3_384 = (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nexports.sha3_512 = (() => gen(0x06, 72, 512 / 8))();\n/** keccak-224 hash function. */\nexports.keccak_224 = (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nexports.keccak_256 = (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nexports.keccak_384 = (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nexports.keccak_512 = (() => gen(0x01, 72, 512 / 8))();\nconst genShake = (suffix, blockLen, outputLen) => (0, utils_ts_1.createXOFer)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\n/** SHAKE128 XOF with 128-bit security. */\nexports.shake128 = (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nexports.shake256 = (() => genShake(0x1f, 136, 256 / 8))();\n//# sourceMappingURL=sha3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@noble/hashes/sha3.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@noble/hashes/utils.js":
/*!*********************************************!*\
  !*** ./node_modules/@noble/hashes/utils.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.wrapXOFConstructorWithOpts = exports.wrapConstructorWithOpts = exports.wrapConstructor = exports.Hash = exports.nextTick = exports.swap32IfBE = exports.byteSwapIfBE = exports.swap8IfBE = exports.isLE = void 0;\nexports.isBytes = isBytes;\nexports.anumber = anumber;\nexports.abytes = abytes;\nexports.ahash = ahash;\nexports.aexists = aexists;\nexports.aoutput = aoutput;\nexports.u8 = u8;\nexports.u32 = u32;\nexports.clean = clean;\nexports.createView = createView;\nexports.rotr = rotr;\nexports.rotl = rotl;\nexports.byteSwap = byteSwap;\nexports.byteSwap32 = byteSwap32;\nexports.bytesToHex = bytesToHex;\nexports.hexToBytes = hexToBytes;\nexports.asyncLoop = asyncLoop;\nexports.utf8ToBytes = utf8ToBytes;\nexports.bytesToUtf8 = bytesToUtf8;\nexports.toBytes = toBytes;\nexports.kdfInputToBytes = kdfInputToBytes;\nexports.concatBytes = concatBytes;\nexports.checkOpts = checkOpts;\nexports.createHasher = createHasher;\nexports.createOptHasher = createOptHasher;\nexports.createXOFer = createXOFer;\nexports.randomBytes = randomBytes;\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nconst crypto_1 = __webpack_require__(/*! @noble/hashes/crypto */ \"(rsc)/./node_modules/@noble/hashes/cryptoNode.js\");\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexports.isLE = (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nexports.swap8IfBE = exports.isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nexports.byteSwapIfBE = exports.swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nexports.swap32IfBE = exports.isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\nexports.nextTick = nextTick;\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await (0, exports.nextTick)();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\nexports.Hash = Hash;\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexports.wrapConstructor = createHasher;\nexports.wrapConstructorWithOpts = createOptHasher;\nexports.wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (crypto_1.crypto && typeof crypto_1.crypto.getRandomValues === 'function') {\n        return crypto_1.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (crypto_1.crypto && typeof crypto_1.crypto.randomBytes === 'function') {\n        return Uint8Array.from(crypto_1.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@noble/hashes/utils.js\n");

/***/ })

};
;