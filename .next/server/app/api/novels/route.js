/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/route";
exports.ids = ["app/api/novels/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/route.ts */ \"(rsc)/./src/app/api/novels/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/route\",\n        pathname: \"/api/novels\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/novels/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n    const genre = searchParams.get(\"genre\");\n    const search = searchParams.get(\"search\");\n    const status = searchParams.get(\"status\") || \"PUBLISHED\";\n    const sortBy = searchParams.get(\"sortBy\") || \"created_at\";\n    const sortOrder = searchParams.get(\"sortOrder\") === \"asc\" ? \"asc\" : \"desc\";\n    const tags = searchParams.get(\"tags\")?.split(\",\").filter(Boolean) || [];\n    const skip = (page - 1) * limit;\n    try {\n        // Build the base query for novels with author information\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).eq(\"status\", status).range(skip, skip + limit - 1);\n        // Add genre filter if provided\n        if (genre) {\n            query = query.eq(\"genre\", genre);\n        }\n        // Add tags filter if provided (novels that contain any of the specified tags)\n        if (tags.length > 0) {\n            query = query.overlaps(\"tags\", tags);\n        }\n        // Add search filter if provided\n        if (search) {\n            query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`);\n        }\n        // Add sorting\n        const sortColumn = sortBy === \"author\" ? \"users.name\" : sortBy;\n        query = query.order(sortColumn, {\n            ascending: sortOrder === \"asc\"\n        });\n        const { data: novels, error: novelsError } = await query;\n        if (novelsError) {\n            console.error(\"Error fetching novels:\", novelsError);\n            throw novelsError;\n        }\n        // Get total count for pagination with same filters\n        let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"status\", status);\n        if (genre) {\n            countQuery = countQuery.eq(\"genre\", genre);\n        }\n        if (tags.length > 0) {\n            countQuery = countQuery.overlaps(\"tags\", tags);\n        }\n        if (search) {\n            countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`);\n        }\n        const { count: total, error: countError } = await countQuery;\n        if (countError) {\n            console.error(\"Error counting novels:\", countError);\n            throw countError;\n        }\n        // Get chapter counts for each novel\n        const novelIds = novels?.map((novel)=>novel.id) || [];\n        let chapterCounts = {};\n        if (novelIds.length > 0) {\n            const { data: chapters, error: chaptersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"novel_id\").in(\"novel_id\", novelIds).eq(\"status\", \"PUBLISHED\");\n            if (!chaptersError && chapters) {\n                chapterCounts = chapters.reduce((acc, chapter)=>{\n                    acc[chapter.novel_id] = (acc[chapter.novel_id] || 0) + 1;\n                    return acc;\n                }, {});\n            }\n        }\n        // Transform snake_case to camelCase and add missing fields\n        const transformedNovels = novels?.map((novel)=>({\n                ...novel,\n                // Convert snake_case to camelCase for consistency with frontend\n                createdAt: novel.created_at,\n                updatedAt: novel.updated_at,\n                authorId: novel.author_id,\n                // Add author data from join\n                author: novel.users ? {\n                    id: novel.users.id,\n                    name: novel.users.name || \"Unknown\",\n                    image: novel.users.image\n                } : {\n                    id: novel.author_id,\n                    name: \"Unknown\",\n                    image: null\n                },\n                _count: {\n                    chapters: chapterCounts[novel.id] || 0\n                }\n            })) || [];\n        // Get available genres for faceted search\n        const { data: genreData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"genre\").eq(\"status\", status).not(\"genre\", \"is\", null);\n        const genreCounts = genreData?.reduce((acc, novel)=>{\n            if (novel.genre) {\n                acc[novel.genre] = (acc[novel.genre] || 0) + 1;\n            }\n            return acc;\n        }, {}) || {};\n        const availableGenres = Object.entries(genreCounts).map(([genre, count])=>({\n                genre,\n                count\n            })).sort((a, b)=>b.count - a.count);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels: transformedNovels,\n            pagination: {\n                page,\n                limit,\n                total: total || 0,\n                pages: Math.ceil((total || 0) / limit),\n                hasNext: page * limit < (total || 0),\n                hasPrev: page > 1\n            },\n            filters: {\n                search,\n                genre,\n                status,\n                tags,\n                sortBy,\n                sortOrder\n            },\n            facets: {\n                genres: availableGenres\n            },\n            metadata: {\n                searchTime: Date.now(),\n                totalResults: total || 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch novels\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags } = body;\n        // Validate required fields\n        if (!title || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate a unique ID for the novel (similar to CUID format)\n        const novelId = `cm${(0,crypto__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(12).toString(\"base64url\")}`;\n        const now = new Date().toISOString();\n        const { data: novel, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").insert({\n            id: novelId,\n            title: title.trim(),\n            description: description?.trim() || null,\n            synopsis: synopsis?.trim() || null,\n            genre: genre?.trim() || null,\n            tags: tags || [],\n            author_id: session.user.id,\n            status: \"DRAFT\",\n            created_at: now,\n            updated_at: now\n        }).select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).single();\n        if (error) {\n            console.error(\"Error creating novel:\", error);\n            throw error;\n        }\n        // Transform the response to match frontend expectations\n        const novelWithCount = {\n            ...novel,\n            // Convert snake_case to camelCase for consistency with frontend\n            createdAt: novel.created_at,\n            updatedAt: novel.updated_at,\n            authorId: novel.author_id,\n            // Add author data from join\n            author: novel.users ? {\n                id: novel.users.id,\n                name: novel.users.name || \"Unknown\",\n                image: novel.users.image\n            } : {\n                id: novel.author_id,\n                name: \"Unknown\",\n                image: null\n            },\n            _count: {\n                chapters: 0\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novelWithCount, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();