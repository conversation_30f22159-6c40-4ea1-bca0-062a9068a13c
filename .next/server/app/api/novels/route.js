/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/route";
exports.ids = ["app/api/novels/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/route.ts */ \"(rsc)/./src/app/api/novels/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/route\",\n        pathname: \"/api/novels\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZub3ZlbHMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRm5vdmVscyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRm5vdmVscyUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRndlZXJhd2F0JTJGRGVza3RvcCUyRmFkYy1wbGF0Zm9ybSUyRnNlcnZpY2VzJTJGY29udGVudCUyRmJsYWNrLWJsb2clMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGd2VlcmF3YXQlMkZEZXNrdG9wJTJGYWRjLXBsYXRmb3JtJTJGc2VydmljZXMlMkZjb250ZW50JTJGYmxhY2stYmxvZyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDNEM7QUFDekg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLz81NmE2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9ub3ZlbHMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9ub3ZlbHNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL25vdmVscy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9ub3ZlbHMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/novels/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n    const genre = searchParams.get(\"genre\");\n    const search = searchParams.get(\"search\");\n    const status = searchParams.get(\"status\") || \"PUBLISHED\";\n    const sortBy = searchParams.get(\"sortBy\") || \"created_at\";\n    const sortOrder = searchParams.get(\"sortOrder\") === \"asc\" ? \"asc\" : \"desc\";\n    const tags = searchParams.get(\"tags\")?.split(\",\").filter(Boolean) || [];\n    const skip = (page - 1) * limit;\n    try {\n        // Build the base query for novels with author information\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).eq(\"status\", status).range(skip, skip + limit - 1);\n        // Add genre filter if provided\n        if (genre) {\n            query = query.eq(\"genre\", genre);\n        }\n        // Add tags filter if provided (novels that contain any of the specified tags)\n        if (tags.length > 0) {\n            query = query.overlaps(\"tags\", tags);\n        }\n        // Add search filter if provided\n        if (search) {\n            query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`);\n        }\n        // Add sorting\n        const sortColumn = sortBy === \"author\" ? \"users.name\" : sortBy;\n        query = query.order(sortColumn, {\n            ascending: sortOrder === \"asc\"\n        });\n        const { data: novels, error: novelsError } = await query;\n        if (novelsError) {\n            console.error(\"Error fetching novels:\", novelsError);\n            throw novelsError;\n        }\n        // Get total count for pagination with same filters\n        let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"status\", status);\n        if (genre) {\n            countQuery = countQuery.eq(\"genre\", genre);\n        }\n        if (tags.length > 0) {\n            countQuery = countQuery.overlaps(\"tags\", tags);\n        }\n        if (search) {\n            countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`);\n        }\n        const { count: total, error: countError } = await countQuery;\n        if (countError) {\n            console.error(\"Error counting novels:\", countError);\n            throw countError;\n        }\n        // Get chapter counts for each novel\n        const novelIds = novels?.map((novel)=>novel.id) || [];\n        let chapterCounts = {};\n        if (novelIds.length > 0) {\n            const { data: chapters, error: chaptersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"novel_id\").in(\"novel_id\", novelIds).eq(\"status\", \"PUBLISHED\");\n            if (!chaptersError && chapters) {\n                chapterCounts = chapters.reduce((acc, chapter)=>{\n                    acc[chapter.novel_id] = (acc[chapter.novel_id] || 0) + 1;\n                    return acc;\n                }, {});\n            }\n        }\n        // Transform snake_case to camelCase and add missing fields\n        const transformedNovels = novels?.map((novel)=>({\n                ...novel,\n                // Convert snake_case to camelCase for consistency with frontend\n                createdAt: novel.created_at,\n                updatedAt: novel.updated_at,\n                authorId: novel.author_id,\n                // Add author data from join\n                author: novel.users ? {\n                    id: novel.users.id,\n                    name: novel.users.name || \"Unknown\",\n                    image: novel.users.image\n                } : {\n                    id: novel.author_id,\n                    name: \"Unknown\",\n                    image: null\n                },\n                _count: {\n                    chapters: chapterCounts[novel.id] || 0\n                }\n            })) || [];\n        // Get available genres for faceted search\n        const { data: genreData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"genre\").eq(\"status\", status).not(\"genre\", \"is\", null);\n        const genreCounts = genreData?.reduce((acc, novel)=>{\n            if (novel.genre) {\n                acc[novel.genre] = (acc[novel.genre] || 0) + 1;\n            }\n            return acc;\n        }, {}) || {};\n        const availableGenres = Object.entries(genreCounts).map(([genre, count])=>({\n                genre,\n                count\n            })).sort((a, b)=>b.count - a.count);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels: transformedNovels,\n            pagination: {\n                page,\n                limit,\n                total: total || 0,\n                pages: Math.ceil((total || 0) / limit),\n                hasNext: page * limit < (total || 0),\n                hasPrev: page > 1\n            },\n            filters: {\n                search,\n                genre,\n                status,\n                tags,\n                sortBy,\n                sortOrder\n            },\n            facets: {\n                genres: availableGenres\n            },\n            metadata: {\n                searchTime: Date.now(),\n                totalResults: total || 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch novels\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags } = body;\n        // Validate required fields\n        if (!title || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        const { data: novel, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").insert({\n            title: title.trim(),\n            description: description?.trim() || null,\n            synopsis: synopsis?.trim() || null,\n            genre: genre?.trim() || null,\n            tags: tags || [],\n            author_id: session.user.id,\n            status: \"DRAFT\"\n        }).select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).single();\n        if (error) {\n            console.error(\"Error creating novel:\", error);\n            throw error;\n        }\n        // Transform the response to match frontend expectations\n        const novelWithCount = {\n            ...novel,\n            // Convert snake_case to camelCase for consistency with frontend\n            createdAt: novel.created_at,\n            updatedAt: novel.updated_at,\n            authorId: novel.author_id,\n            // Add author data from join\n            author: novel.users ? {\n                id: novel.users.id,\n                name: novel.users.name || \"Unknown\",\n                image: novel.users.image\n            } : {\n                id: novel.author_id,\n                name: \"Unknown\",\n                image: null\n            },\n            _count: {\n                chapters: 0\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novelWithCount, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/@babel","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();